<template>
  <!-- 新增跟进记录表单 -->
  <div class="submit-panel">
    <div class="form" :class="openSubmitPanel ? 'is-open' : 'is-close'" @click="handleFormClick">
      <template v-if="openSubmitPanel">
        <div class="tool">
          <el-select style="width: 150px" v-model="form.followType">
            <el-option v-for="item in followTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-date-picker
            v-model="form.nextContactTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 250px; margin: 0 10px"
            :disabled-date="(time: any) => {  return time.getTime() < Date.now() - 8.64e7}"
            placeholder="下次联系时间"
          />
        </div>
        <div class="textarea">
          <el-input
            type="textarea"
            v-model="form.content"
            maxlength="200"
            :autofocus="openSubmitPanel"
            :rows="5"
            placeholder="请输入跟进内容"
          />
        </div>
      </template>
    </div>
    <div class="footer" v-if="openSubmitPanel">
      <el-button type="primary" v-loading="submitLoading" @click="onSubmit">发布</el-button>
      <el-button @click="openSubmitPanel = false" v-loading="submitLoading">取消</el-button>
    </div>
  </div>

  <!-- 跟进记录表格 -->
  <ZCustomTable ref="followTableRef" page-key="crmFollowRecord" :config="followConfig" @get-list="getFollowList">
    <template #table="scope">
      <template v-if="scope.list.length">
        <div v-for="item in scope.list" class="visit-item" :key="item">
          <div class="visit-item-user">
            <div class="visit-item-user-avatar">
              <el-avatar :src="item.avatar || defaultAvatar" :size="30"/>
            </div>
            <div class="visit-item-user-info">
              <div class="visit-item-user-info-top">
                <div class="visit-item-user-info-top-name">{{ item.customerName }}</div>
                <div class="visit-item-user-info-top-time">{{ item.createTime }}</div>
              </div>
              <div class="visit-item-user-info-bottom">
                跟进记录 | <DictTag cssClass="text" :type="DICT_TYPE.CRM_FOLLOW_TYPE" :value="item.followType" />
              </div>
            </div>
            <div class="visit-item-user-btn">
              <el-button type="primary" link @click="handleEdit(item)">编辑</el-button>
              <el-button type="danger" link @click="handleDelete(item.id)">删除</el-button>
            </div>
          </div>
          <div class="visit-item-form">
            <el-row :gutter="20">
              <el-col :span="24" v-if="item.nextContactTime">
                <div class="visit-item-form-item">
                  <div class="visit-item-form-item-label">下次联系时间：</div>
                  <div class="visit-item-form-item-value">{{ item.nextContactTime }}</div>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="visit-item-form-item">
                  <div class="visit-item-form-item-label">跟进内容：</div>
                  <div class="visit-item-form-item-value" v-html="item.content"></div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>
      <el-empty v-else class="!h-full" image-size="100"/>
    </template>
  </ZCustomTable>
  <FormPanel ref="formPanelRef" @success="followTableRef?.reload()"/>
</template>

<script setup lang="ts">
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { DictTag } from '@/components/DictTag'
import { ZCustomTable } from '@/components/Zeadoor/CustomTable'
import { ConfigBO } from '@/components/Zeadoor/CustomTable/config'
import * as FollowApi from '@/api/crm/follow/index'
import FormPanel from "./follow-form.vue"
import defaultAvatar from "@/assets/imgs/avatar.jpg";

// 消息弹窗
const message = useMessage()
const formPanelRef = ref()
const props = defineProps<{
  targetType: string
  targetId: number
}>()

const emits = defineEmits(['success'])

// 表格引用
const followTableRef = ref()

// 字典选项
const followTypeOptions = ref<any[]>([])

// 跟进记录表格配置
const followConfig = ref<ConfigBO>({
  table: {
    showSelection: false,
    columns: [],
    toolbar: {
      show: true,
      label: '操作',
      width: 120,
      fixed: 'right',
      buttons: [
        {
          key: 'edit',
          label: '编辑',
          type: 'primary',
          link: true,
          click: (scope: any) => {
            handleEdit(scope.row)
          }
        },
        {
          key: 'delete',
          label: '删除',
          type: 'danger',
          link: true,
          click: (scope: any) => {
            handleDelete(scope.row.id)
          }
        }
      ]
    }
  },
  search: {
    show: false
  }
})

// 获取字典数据
const getFollowTypeOptions = () => {
  followTypeOptions.value = getDictOptions(DICT_TYPE.CRM_FOLLOW_TYPE) || []
}

// 获取跟进记录列表
const getFollowList = async (queryParams: any, done: Function, error: Function) => {
  try {
    // 添加目标类型和ID参数
    const params = {
      ...queryParams,
      targetType: props.targetType,
      targetId: props.targetId
    }
    const data = await FollowApi.getFollowRecordPage(params)
    done(data.list || [], data.total || 0)
  } catch (err) {
    error()
  }
}

const submitLoading = ref(false)
const openSubmitPanel = ref(false)
const form = ref({
  id: undefined,
  followType: '10',
  nextContactTime: null,
  content: null,
  targetType: props.targetType,
  targetId: props.targetId,
  fileIdList: null
})

const onSubmit = async () => {
  if (!form.value.content) {
    message.error('请输入跟进内容')
    return
  }
  submitLoading.value = true
  try {
    if (form.value.id) {
      // 编辑
      await FollowApi.updateFollowRecord(form.value)
      message.success('跟进记录更新成功')
    } else {
      // 新增
      await FollowApi.createFollowRecord(form.value)
      message.success('跟进记录添加成功')
    }
    // 提交成功后关闭表单并重置
    openSubmitPanel.value = false
    emits('success')
    resetForm()
    // 刷新跟进记录表格
    followTableRef.value?.reload()
  } catch (error) {
    console.error('提交跟进记录失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const resetForm = () => {
  form.value = {
    id: undefined,
    followType: '10',
    nextContactTime: null,
    content: null,
    targetType: props.targetType,
    targetId: props.targetId,
    fileIdList: null
  }
}

// 处理表单点击事件，避免在已打开状态下重复触发
const handleFormClick = () => {
  // 如果表单已经打开，不做任何处理，避免清除内容
  if (openSubmitPanel.value) {
    return
  }
  // 只有在表单关闭状态下才打开表单
  openSubmitPanel.value = true
}

// 编辑跟进记录
const handleEdit = (row: any) => {
  formPanelRef.value.open(row)
}

// 删除跟进记录
const handleDelete = async (id: number) => {
  await message.confirm('确认删除吗?')
  await FollowApi.deleteFollowRecord(id)
  message.success('删除成功')
  // 刷新跟进记录表格
  followTableRef.value?.reload()
}

// 组件挂载时初始化
onMounted(() => {
  getFollowTypeOptions()
})
</script>

<style scoped lang="scss">
.submit-panel {
  display: flex;
  margin-bottom: 10px;
  flex-flow: wrap row;

  .form {
    border: 1px solid var(--el-border-color-light);
    flex: 1;
    border-radius: 5px;
    position: relative;
    min-height: 40px;

    &.is-close:before {
      content: '写跟进...';
      position: absolute;
      top: 50%;
      left: 20px;
      transform: translate(0, -50%);
      color: var(--el-text-color-disabled);
    }

    .tool {
      display: flex;
      padding: 10px;

      :deep(.el-upload__tip) {
        display: none;
      }

      :deep(.file-upload) {
        display: flex;

        .el-upload-list {
          margin-top: 0;
          flex: 1;
          display: flex;

          .el-upload-list__item {
            margin: 0;
            display: flex;
            align-items: center;
            width: auto !important;
          }
        }
      }
    }

    .textarea {
      margin: 0 10px;

      :deep(.el-textarea__inner) {
        box-shadow: 0 0 0 #eee;
      }
    }
  }

  .footer {
    width: 100%;
    padding-left: 0px;
    margin-top: 10px;
  }
}

.visit-item {
  cursor: pointer;
  margin-bottom: 20px;
  border-bottom: 1px dashed var(--el-border-color-light);
  padding-bottom: 10px;

  &.detail {
    .visit-item-user {
      border-bottom: 1px dashed var(--border-bb);
    }
  }

  &.record {
    border-bottom: 1px dashed var(--border-bb);
    margin-bottom: 10px;
  }

  &:hover {
    .visit-item-user {
      .visit-item-user-btn {
        display: flex;
      }
    }
  }

  .visit-item-user {
    display: flex;
    align-items: center;
    padding: 10px 0 20px 0;

    .visit-item-user-info {
      flex: 1;
      margin-left: 20px;

      .visit-item-user-info-top {
        display: flex;
        align-items: center;

        .visit-item-user-info-top-name {
          color: var(--el-color-primary);
        }

        .visit-item-user-info-top-time {
          font-size: 12px;
          margin-left: 10px;
          color: var(--el-text-color-secondary);
        }
      }

      .visit-item-user-info-bottom {
        font-size: 13px;
        margin-top: 10px;
        color: var(--el-text-color-regular);

        :deep(.-fac-readonly) {
          font-weight: 500;
        }
      }
    }

    .visit-item-user-btn {
      display: none;
      align-items: center;

      .el-button--danger {
        color: red !important;
      }

      .visit-item-user-btn-comment {
        color: var(--font-hb);
        margin-left: 20px;
        margin-top: -2px;
        cursor: pointer;

        &:hover {
          color: var(--primary);
        }

        display: flex;
        align-items: center;

        font {
          font-size: 14px;
        }
      }
    }
  }
}

.visit-item-form {
  .visit-item-form-item {
    display: flex;
    font-size: 13px;
    margin-bottom: 15px;

    .visit-item-form-item-label {
      width: 100px;
      color: var(--el-text-color-secondary);
      flex-shrink: 0;
    }

    .visit-item-form-item-value {
      flex: 1;

      &.is-target {
        color: var(--el-color-primary);
      }

      .more {
        color: var(--primary);
        cursor: pointer;
      }

      .more-icon {
        display: inline-grid;
        margin-left: 4px;
        transform: rotate(90deg);

        &.active {
          transform: rotate(270deg);
        }
      }
    }
  }
}
</style>
