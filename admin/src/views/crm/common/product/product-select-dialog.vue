<template>
  <el-dialog v-model="dialogVisible" title="选择产品" width="1200px" :close-on-click-modal="false"
             destroy-on-close class="product-select-dialog">
    <div class="dialog-content">
      <!-- 左侧产品列表 -->
      <div class="left-panel">
        <ZCustomTable ref="tableRef" page-key="crmProductSelect" :config="config" @get-list="getList"
                      @select="onSelect" @select-all="onSelectAll">
          <template #column-prop-status="scope">
            <el-tag :type="scope.row.status ? 'success' : 'info'">
              {{ scope.row.status ? '已上架' : '已下架' }}
            </el-tag>
          </template>
          <template #empty>
            <div class="empty-data">
              <el-empty description="暂无数据" />
            </div>
          </template>
        </ZCustomTable>
      </div>

      <!-- 右侧已选择面板 -->
      <div class="right-panel">
        <div class="selected-data-panel">
          <div class="selected-data-panel_title">
            <div class="count">已选({{ selectIdList.length }})</div>
            <div class="clear" @click="clearSelect">
              <i class="vbase vbase-delete-fill"></i>清空
            </div>
          </div>
          <div class="selected-data-panel-body" v-loading="selectLoading">
            <el-scrollbar v-if="selectList.length">
              <div class="data-item" v-for="(item, index) in selectList" :key="item.id">
                <div class="data-item_label">{{ item.name }}</div>
                <div class="data-item_icon" @click="removeSelect(item, index)">
                  <el-icon>
                    <Delete/>
                  </el-icon>
                </div>
              </div>
            </el-scrollbar>
            <div v-else class="page-empty">
              <el-empty description="暂未选择数据"/>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :disabled="!selectList.length" @click="confirmSelection">
          确认({{ selectList.length }})
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Delete } from '@element-plus/icons-vue'
import { ConfigBO } from '@/components/Zeadoor/CustomTable/config'
import { getProductPage } from '@/api/crm/product'
import { ColumnTypeVo } from '@/components/Zeadoor/interface'
import { DICT_TYPE } from '@/utils/dict'

const emit = defineEmits(['select'])

const dialogVisible = ref(false)

const tableRef = ref()
const list = ref([]) // 当前页面数据

// 已选择相关变量
const selectIdList = ref<any[]>([])
const selectList = ref<any[]>([])
const selectLoading = ref(false)

// 表格配置
const config = ref<ConfigBO>({
  table: {
    toolbar: {
      show: false
    },
    showSelection: true,
    columns: [
      {
        label: '产品名称',
        prop: 'name',
        width: 160
      },
      {
        label: '产品类型',
        prop: 'categoryName',
        width: 130
      },
      {
        label: '产品单位',
        prop: 'unit',
        type: ColumnTypeVo.dict,
        dictKey: DICT_TYPE.CRM_PRODUCT_UNIT,
        width: 100
      },
      {
        label: '产品编码',
        prop: 'no',
        width: 120
      },
      {
        label: '价格',
        prop: 'price',
        width: 150
      },
      {
        label: '产品描述',
        prop: 'description',
        width: 200
      },
      {
        label: '状态',
        prop: 'status',
        width: 100
      },
      {
        label: '负责人',
        prop: 'leaderName',
        width: 120
      },
      {
        label: '所属部门',
        prop: 'deptName',
        width: 200
      },
      {
        label: '更新时间',
        prop: 'updateTime',
        minWidth: 160
      },
      {
        label: '创建时间',
        prop: 'createTime',
        minWidth: 160
      },
      {
        label: '创建人',
        prop: 'creatorName',
        minWidth: 120
      }
    ]
  },
  search: {
    show: true,
    showMore: false
    // conditions: [
    //   {
    //     key: 'keyword',
    //     label: '产品名称/编码',
    //     type: 'input',
    //     placeholder: '请输入产品名称或编码'
    //   },
    //   {
    //     key: 'typeId',
    //     label: '产品类型',
    //     type: 'select',
    //     options: []
    //   },
    //   {
    //     key: 'status',
    //     label: '状态',
    //     type: 'select',
    //     options: [
    //       { label: '已上架', value: true },
    //       { label: '已下架', value: false }
    //     ]
    //   }
    // ]
  }
})

/**
 * 获取列表数据
 */
const getList = async (queryParams: any, done: Function, error: Function) => {
  try {
    queryParams.status = "10"
    const data = await getProductPage(queryParams)
    list.value = data.list
    done(data.list, data.total)

    if (selectList.value && selectList.value.length) {
      await nextTick(() => {
        list.value.forEach((t: any) => {
          if (selectList.value.find((s: any) => s.id === t.id)) {
            tableRef.value?.handleRowSelection(t, true)
          }
        })
      })
    }
  } catch (e) {
    error()
  }
}

/**
 * 确认选择
 */
const confirmSelection = () => {
  emit('select', selectList.value)
  dialogVisible.value = false
}

/**
 * 打开弹窗
 * @param preSelectedProducts 已选产品列表
 */
const open = (preSelectedProducts: any[]) => {
  dialogVisible.value = true

  // 初始化选择状态
  selectList.value = [...preSelectedProducts]
  selectIdList.value = preSelectedProducts.map(item => item.id)
}

/**
 * 关闭弹窗
 */
const close = () => {
  dialogVisible.value = false
  selectList.value = []
  selectIdList.value = []
}

/**
 * 单个选择处理
 */
const onSelect = (selection: any, row: any) => {
  const selected = selection.some((item: any) => item.id === row.id)
  const index = selectList.value.findIndex((item: any) => item.id === row.id)
  if (selected && index === -1) {
    // 如果在 selection 中存在，且在 selectList 中不存在，则添加
    selectList.value.push(row)
  } else if (!selected && index !== -1) {
    // 如果在 selection 中不存在，且在 selectList 中存在，则移除
    selectList.value.splice(index, 1)
  }
  selectIdList.value = selectList.value.map((item) => item.id)
}

/**
 * 全选处理
 */
const onSelectAll = (selection: any) => {
  // const currentPageIds = list.value.map((item: any) => item.id)
  // 处理当前页的选择
  list.value.forEach((item: any) => {
    const isSelected = selection.some((selectItem: any) => selectItem.id === item.id)
    const indexInSelectList = selectList.value.findIndex((sItem) => sItem.id === item.id)
    if (isSelected && indexInSelectList === -1) {
      // 在当前页被选中，且不在总列表中，则添加
      selectList.value.push(item)
    } else if (!isSelected && indexInSelectList !== -1) {
      // 在当前页被取消选中，且在总列表中，则移除
      selectList.value.splice(indexInSelectList, 1)
    }
  })

  // 更新ID列表
  selectIdList.value = selectList.value.map((item) => item.id)
}

/**
 * 移除单个选择
 */
const removeSelect = (item: any, index: any) => {
  selectList.value.splice(index, 1)
  selectIdList.value.splice(selectIdList.value.indexOf(item.id), 1)
  // 如果移除的项在当前页，则取消表格的选中状态
  const rowInTable = list.value.find((t: any) => t.id === item.id)
  if (rowInTable) {
    tableRef.value?.handleRowSelection(rowInTable, false)
  }
}

/**
 * 清空所有选择
 */
const clearSelect = function () {
  selectList.value = []
  selectIdList.value = []
  tableRef.value.reload()
}

// 暴露方法给父组件
defineExpose({
  open,
  close
})

</script>

<style lang="scss" scoped>
.product-select-dialog {
  .dialog-content {
    display: flex;
    gap: 20px;
    height: 500px;

    .left-panel {
      flex: 1;
      min-width: 0;
    }

    .right-panel {
      flex-shrink: 0;

      .selected-data-panel {
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        height: 100%;
        display: flex;
        flex-direction: column;

        .selected-data-panel_title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid #e4e7ed;
          background-color: #f5f7fa;

          .count {
            font-weight: 500;
            color: #303133;
          }

          .clear {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #f56c6c;
            cursor: pointer;
            font-size: 14px;

            &:hover {
              color: #f78989;
            }
          }
        }

        .selected-data-panel-body {
          flex: 1;
          overflow: hidden;

          .data-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;

            &:hover {
              background-color: #f5f7fa;
            }

            .data-item_label {
              flex: 1;
              font-size: 14px;
              color: #303133;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .data-item_icon {
              color: #f56c6c;
              cursor: pointer;
              padding: 4px;

              &:hover {
                color: #f78989;
              }
            }
          }

          .page-empty {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
