<template>
  <ZCustomFormDialog ref="formRef"
                     v-model:rules="formRules"
                     :config="{form: {defaultVal: defaultVal, disabledProps: disabledProps}}"
                     page-key="crmInvoice"
                     @get-detail="getDetail"
                     @on-save="onSave"
                     :is-custom="true">
    <!-- 合同金额 -->
    <template #form-prop-contractAmount="scope">
      {{ scope.form.contractAmount || 0 }}
    </template>

    <!-- 客户名称 -->
    <template #form-prop-customerId="scope">
      <el-select v-model="scope.form.customerId" filterable show-arrow clearable placeholder="请选择客户名称"
                 :disabled="scope.disabled" @change="handleCustomerChange(scope.form)"
                 remote :remote-method="(query) => getCustomerSelectList(query)">
        <el-option v-for="item in getSelectRemoteFullList(customerSelectList, scope.form.customerId, scope.form.customerName)"
                   :key="item.id" :label="item.name" :value="item.id"/>
      </el-select>
    </template>
    <!-- 合同名称 -->
    <template #form-prop-contractId="scope">
      <el-select v-model="scope.form.contractId" filterable show-arrow clearable placeholder="请选择合同名称"
                 :disabled="scope.disabled"  @change="handleContractChange(scope.form)"
                 remote :remote-method="(query) => getContractSelectList(scope.form.customerId, query)">
        <el-option v-for="item in getSelectRemoteFullList(contractSelectList, scope.form.contractId, scope.form.contractName)"
                   :key="item.id" :label="item.name" :value="item.id"/>
      </el-select>
    </template>

    <template #form-prop-titleType="scope">
      <el-select v-model="scope.form.titleType" placeholder="请选择抬头类型" class="w-full">
        <el-option :value="'10'" label="单位"/>
        <el-option :value="'20'" label="个人"/>
      </el-select>
    </template>

    <!-- 选择发票信息 -->
    <template #form-prop-fpSelBut="scope">
      <el-button
        type="default"
        plain
        :disabled="!scope.form.customerId"
        @click="openInvoiceHeaderDialog(scope.form)"
        class="invoice-select-btn">
        <Icon icon="ep:plus" class="mr-5px" />选择发票信息
      </el-button>
    </template>
  </ZCustomFormDialog>

  <!-- 发票抬头选择对话框 -->
  <InvoiceHeaderDialog ref="invoiceHeaderDialogRef" @select="handleInvoiceHeaderSelect" />
</template>
<script lang="ts" setup>
import * as MainApi from '@/api/crm/invoice/index'
import {getSelectRemoteFullList} from "@/utils/dict";
import { getContractList, getContract, getContractPage } from '@/api/crm/contract'
import {propTypes} from "@/utils/propTypes";
import {customerSelectList, getCustomerSelectList} from "@/api/crm/common";
import InvoiceHeaderDialog from '@/views/crm/common/invoice/invoice-header-dialog.vue';

const props = defineProps({
  defaultVal: propTypes.object.def(),
  disabledProps: propTypes.object.def([])
})
const emits = defineEmits(['complete'])
const formRef = ref()
const formRules = ref()

// 发票抬头对话框控制
const invoiceHeaderDialogRef = ref()
const currentForm = ref()

/** 查询详情 */
const getDetail = async (id: number, done: Function, error: Function) => {
  try {
    const data = await MainApi.getInvoice(id)
    done(data)
  } catch {
    error()
  }
}

/**保存数据*/
const onSave = async (formData: any, done: Function, error: Function) => {
  try {
    if (!formData.id) {
      await MainApi.createInvoice(formData)
    } else {
      await MainApi.updateInvoice(formData)
    }
    emits("complete")
    done()
  } catch {
    error()
  }
}

// 清空合同选择
const clearContract = (form: any) => {
  if (form) {
    form.contractId = null
    form.contractAmount = null
  }
  contractSelectList.value = []
}

// 处理客户选择变化
const handleCustomerChange = (form: any) => {
  if (form) {
    // 清空合同
    clearContract(form)
    if (form.customerId) {
      // 获取该客户的合同列表
      getContractSelectList(form.customerId)
    }
  }
}

// 处理合同选择变化
const handleContractChange = async (form: any) => {
  if (form && form.contractId) {
    try {
      // 获取合同详情，填充金额字段
      const contract = await getContract(form.contractId)
      if (contract && contract.amount) {
        form.contractAmount = contract.amount
      }
    } catch (error) {
      console.error('获取合同详情失败', error)
    }
  }
}

// 获取合同选择列表
const contractSelectList = ref([])
const getContractSelectList = async (customerId?: number, searchKey?: string) => {
  try {
    const data = await getContractPage({
      customerId: customerId,
      name: searchKey,
      approvalStatus:2
    })
    contractSelectList.value = data.list
  } finally {
  }
}

// 打开发票抬头选择对话框
const openInvoiceHeaderDialog = (form: any) => {
  if (!form.customerId) {
    return
  }
  currentForm.value = form
  invoiceHeaderDialogRef.value.open(form.customerId)
}

// 处理发票抬头选择
const handleInvoiceHeaderSelect = (row: any) => {
  if (currentForm.value) {
    // 将选中的发票抬头信息赋值给表单
    currentForm.value.titleType = row.titleType
    currentForm.value.invoiceTitle = row.invoiceTitle
    currentForm.value.taxNumber = row.taxNumber
    currentForm.value.bankName = row.bankName
    currentForm.value.bankAccount = row.bankAccount
    currentForm.value.phone = row.phone
    currentForm.value.address = row.address
  }
}

defineExpose({
  open: (id?: number) => {
    formRef.value.open(id)
    // 获取客户列表
    // getCustomerSelectList()
    // // 获取合同列表
    // getContractSelectList()
  }
})
</script>

<style lang="scss" scoped>
.invoice-select-btn {
  width: 100%;
  height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #ffffff;
  color: #606266;
  font-size: 14px;

  &:hover {
    border-color: #c0c4cc;
    background-color: #f5f7fa;
  }

  &:disabled {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
  }

  .el-icon {
    color: #909399;
  }
}
</style>
